app:
  description: An agent that can access real time info
  icon: 🔍
  icon_background: '#E0F2FE'
  mode: agent-chat
  name: Agent with search
  use_icon_as_answer_icon: false
kind: app
model_config:
  agent_mode:
    enabled: true
    max_iteration: 5
    prompt: null
    strategy: function_call
    tools:
    - enabled: true
      isDeleted: false
      notAuthor: false
      provider_id: jina
      provider_name: jina
      provider_type: builtin
      tool_label: JinaReader
      tool_name: jina_reader
      tool_parameters:
        gather_all_images_at_the_end: 0
        gather_all_links_at_the_end: true
        image_caption: true
        max_retries: 3
        no_cache: 0
        proxy_server: null
        request_params: null
        summary: 0
        target_selector: null
        url: null
        wait_for_selector: null
    - enabled: true
      isDeleted: false
      notAuthor: false
      provider_id: stackexchange
      provider_name: stackexchange
      provider_type: builtin
      tool_label: Search Stack Exchange Questions
      tool_name: searchStackExQuestions
      tool_parameters:
        accepted: 'true'
        intitle: null
        nottagged: null
        order: desc
        pagesize: 10
        site: null
        sort: null
        tagged: null
    - enabled: true
      isDeleted: false
      notAuthor: false
      provider_id: stackexchange
      provider_name: stackexchange
      provider_type: builtin
      tool_label: Fetch Stack Exchange Answers
      tool_name: fetchAnsByStackExQuesID
      tool_parameters:
        filter: ''
        id: ''
        order: ''
        page: ''
        pagesize: ''
        site: ''
        sort: ''
    - enabled: true
      provider_id: jina
      provider_name: jina
      provider_type: builtin
      tool_label: Search the web
      tool_name: jina_search
      tool_parameters:
        gather_all_images_at_the_end: ''
        gather_all_links_at_the_end: ''
        image_caption: ''
        max_retries: ''
        no_cache: ''
        proxy_server: ''
        query: ''
  annotation_reply:
    enabled: false
  chat_prompt_config: {}
  completion_prompt_config: {}
  dataset_configs:
    datasets:
      datasets: []
    retrieval_model: multiple
  dataset_query_variable: ''
  external_data_tools: []
  file_upload:
    image:
      detail: high
      enabled: true
      number_limits: 3
      transfer_methods:
      - remote_url
      - local_file
  model:
    completion_params:
      stop: []
    mode: chat
    name: claude-3-5-sonnet-20240620
    provider: anthropic
  more_like_this:
    enabled: false
  opening_statement: Hello, I'm Information Sage, Skilled in information retrieval
    and content. Let's start chatting!
  pre_prompt: 'You are a helpful agent that has internet access. Specifically, you
    can:


    1. search for something

    2. read contents from a given URL


    When answering user''s questions, you can use these abilities when needed, to
    help yourself to provide better answer to user'
  prompt_type: simple
  retriever_resource:
    enabled: true
  sensitive_word_avoidance:
    configs: []
    enabled: false
    type: ''
  speech_to_text:
    enabled: false
  suggested_questions: []
  suggested_questions_after_answer:
    enabled: false
  text_to_speech:
    enabled: false
    language: ''
    voice: ''
  user_input_form: []
version: 0.1.2
