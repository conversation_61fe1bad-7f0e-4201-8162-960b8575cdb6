app:
  description: Check if the Resume matches the Job Description, using the method taught
    by Mark
  icon: 📄
  icon_background: '#D5F5F6'
  mode: workflow
  name: JD & Resume Matching Flow
  use_icon_as_answer_icon: false
kind: app
version: 0.1.2
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        sourceType: start
        targetType: llm
      id: 1728189789518-source-1728189832414-target
      selected: false
      source: '1728189789518'
      sourceHandle: source
      target: '1728189832414'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: start
        targetType: llm
      id: 1728189789518-source-1728190110426-target
      selected: false
      source: '1728189789518'
      sourceHandle: source
      target: '1728190110426'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: start
        targetType: llm
      id: 1728189789518-source-1728190596485-target
      selected: false
      source: '1728189789518'
      sourceHandle: source
      target: '1728190596485'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: llm
      id: 1728190930023-source-1728190946806-target
      selected: false
      source: '1728190930023'
      sourceHandle: source
      target: '1728190946806'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: llm
      id: 1728189832414-source-1728190946806-target
      selected: false
      source: '1728189832414'
      sourceHandle: source
      target: '1728190946806'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: end
      id: 1728190946806-source-1728190903113-target
      selected: false
      source: '1728190946806'
      sourceHandle: source
      target: '1728190903113'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: template-transform
      id: 1728190110426-source-1728224784436-target
      selected: false
      source: '1728190110426'
      sourceHandle: source
      target: '1728224784436'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: template-transform
      id: 1728190596485-source-1728224784436-target
      selected: false
      source: '1728190596485'
      sourceHandle: source
      target: '1728224784436'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: template-transform
        targetType: llm
      id: 1728224784436-source-1728190930023-target
      selected: false
      source: '1728224784436'
      sourceHandle: source
      target: '1728190930023'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: Start
        type: start
        variables:
        - label: Job Descroption
          max_length: 10000
          options: []
          required: true
          type: paragraph
          variable: jd
        - label: Resume
          max_length: 10000
          options: []
          required: true
          type: paragraph
          variable: resume
      height: 114
      id: '1728189789518'
      position:
        x: 80
        y: 282
      positionAbsolute:
        x: 80
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4o-2024-08-06
          provider: openai
        prompt_template:
        - id: 15c89f4c-01aa-4b48-a78e-751366831e1e
          role: system
          text: "## Instructions\n\nTo assess the extent to which a resume matches\
            \ a job description, follow these steps:\n\n1. **Extract Key Requirements**:\
            \ Begin by identifying and extracting the key requirements, skills, qualifications,\
            \ and experiences listed in the job description. Pay attention to specific\
            \ technical skills, years of experience, educational background, and any\
            \ other criteria that are emphasized.\n\n2. **Analyze the Resume**: Examine\
            \ the resume to identify the candidate's skills, experiences, and qualifications.\
            \ Note any specific achievements, roles, and responsibilities that align\
            \ with the job description.\n\n3. **Compare and Contrast**: Compare the\
            \ extracted elements from the job description with the details in the\
            \ resume. Look for direct matches, partial matches, and any missing elements.\
            \ Consider both the quantity and quality of matches.\n\n4. **Evaluate\
            \ the Match**: Assess the extent of the match by considering the following:\n\
            \   - **Full Match**: All key requirements from the job description are\
            \ met or exceeded by the resume.\n   - **Partial Match**: Some key requirements\
            \ are met, but there are notable gaps.\n   - **Minimal Match**: Few or\
            \ none of the key requirements are met.\n\n5. **Provide a Summary**: Summarize\
            \ the findings in a clear and concise manner. Highlight the strengths\
            \ and weaknesses of the resume in relation to the job description.\n\n\
            6. **Avoid XML Tags**: Ensure that the output does not contain any XML\
            \ tags or formatting.\n\n## Examples\n\n### Example 1\n\n**Job Description**\
            \  \nSeeking a software engineer with 5+ years of experience in Java,\
            \ Python, and cloud technologies. Must have a Bachelor's degree in Computer\
            \ Science and experience with agile methodologies.\n\n**Resume**  \nCandidate\
            \ has 6 years of experience in Java and Python, a Bachelor's degree in\
            \ Computer Science, and has worked in agile environments. No specific\
            \ mention of cloud technologies.\n\n**Output**  \nThe resume is a strong\
            \ match for the job description, meeting most requirements except for\
            \ specific experience with cloud technologies.\n\n### Example 2\n\n**Job\
            \ Description**  \nLooking for a marketing manager with expertise in digital\
            \ marketing, SEO, and content creation. Requires 3+ years of experience\
            \ and a degree in Marketing or related field.\n\n**Resume**  \nApplicant\
            \ has 4 years of experience in digital marketing and content creation,\
            \ with a degree in Communications. Limited experience in SEO.\n\n**Output**\
            \  \nThe resume partially matches the job description, with strengths\
            \ in digital marketing and content creation, but lacks depth in SEO."
        - id: 7de89efd-a809-444a-86ad-9c3c032fde39
          role: user
          text: 'Here is the Job Description:


            ---


            {{#1728189789518.jd#}}


            ---


            Here is the resume:


            ---


            {{#1728189789518.resume#}}


            ---'
        selected: false
        title: Direct Match LLM
        type: llm
        variables: []
        vision:
          configs:
            detail: high
          enabled: true
      height: 96
      id: '1728189832414'
      position:
        x: 448
        y: 36.82526874494364
      positionAbsolute:
        x: 448
        y: 36.82526874494364
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: true
          variable_selector:
          - '1728189789518'
          - jd
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: claude-3-5-sonnet-20240620
          provider: anthropic
        prompt_template:
        - id: a3eb1aee-26b8-4070-9822-36a621937d1d
          role: system
          text: "## Instructions\n\nTo complete the task of extracting technical keywords\
            \ from a job description and sorting them based on importance, follow\
            \ these steps:\n\n1. **Read the Job Description**: Carefully read through\
            \ the provided job description to understand the context and identify\
            \ potential technical keywords.\n\n2. **Identify Technical Keywords**:\
            \ Look for specific technical terms that are commonly associated with\
            \ skills, tools, technologies, or platforms. Examples include programming\
            \ languages (e.g., Java, Python), frameworks (e.g., Flask, React), cloud\
            \ services (e.g., AWS, Azure), databases (e.g., MySQL, PostgreSQL), version\
            \ control systems (e.g., Git), and tools (e.g., Git, Docker, Maven).\n\
            \n3. **Assess Importance**: Determine the importance of each keyword based\
            \ on its frequency of mention, the context in which it appears, and any\
            \ explicit emphasis in the job description (e.g., \"must-have\" skills\
            \ or \"preferred\" technologies).\n\n4. **Sort Keywords**: Arrange the\
            \ identified keywords in order of importance, starting with the most critical\
            \ or frequently mentioned ones.\n\n5. **Format Output**: Present the sorted\
            \ keywords as a comma-separated list. Ensure that the output is clear\
            \ and free of any XML tags or additional formatting.\n\n6. **Review**:\
            \ Double-check the list to ensure all relevant keywords are included and\
            \ correctly prioritized.\n\n## Examples\n\n### Example 1\n\n**Job Description**\
            \  \nWe are looking for a skilled software engineer with experience in\
            \ Java, Spring Boot, and AWS. Familiarity with Docker and Git is a plus.\
            \ The candidate should also have knowledge of MySQL and RESTful APIs.\n\
            \n**Keywords**  \nJava, Spring Boot, AWS, Docker, Git, MySQL, RESTful\
            \ APIs\n\n### Example 2\n\n**Job Description**  \nJoin our team as a DevOps\
            \ engineer. Required skills include Kubernetes, Terraform, and Jenkins.\
            \ Experience with AWS and Python scripting is essential. Knowledge of\
            \ Ansible and Docker is advantageous.\n\n**Keywords**  \nKubernetes, Terraform,\
            \ Jenkins, AWS, Python, Ansible, Docker\n\n### Example 3\n\n**Job Description**\
            \  \nWe need a data scientist proficient in Python, TensorFlow, and SQL.\
            \ Experience with AWS and data visualization tools like Tableau is preferred.\
            \ Familiarity with Git and Docker is beneficial.\n\n**Keywords**  \nPython,\
            \ TensorFlow, SQL, AWS, Tableau, Git, Docker"
        - id: ************************************
          role: user
          text: '{{#context#}}'
        selected: false
        title: JD Keyword Extractor
        type: llm
        variables: []
        vision:
          configs:
            detail: high
          enabled: true
      height: 96
      id: '1728190110426'
      position:
        x: 448
        y: 282
      positionAbsolute:
        x: 448
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: true
          variable_selector:
          - '1728189789518'
          - resume
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: claude-3-5-sonnet-20240620
          provider: anthropic
        prompt_template:
        - id: b7f32a9f-7304-4373-ad7f-dedca8e53823
          role: system
          text: "## Instructions\n\nTo complete the task of extracting technical keywords\
            \ from a resume, follow these steps:\n\n1. Carefully read through the\
            \ provided resume text to identify any technical terms or technologies\
            \ mentioned. These may include programming languages, frameworks, tools,\
            \ platforms, databases, and other relevant technical skills.\n\n2. Focus\
            \ on identifying keywords that are commonly recognized in the tech industry.\
            \ Examples include programming languages like Java, Python, and C++; frameworks\
            \ such as Flask and React; cloud services like AWS and Azure; databases\
            \ like MySQL and MongoDB; and tools such as Git, Docker, and Maven.\n\n\
            3. Ensure that the extracted keywords are specific and relevant to the\
            \ technical domain. Avoid general terms that do not specifically pertain\
            \ to technology or technical skills.\n\n4. Once you have identified the\
            \ technical keywords, compile them into a list.\n\n5. Format the list\
            \ of keywords as a comma-separated string. Ensure there are no additional\
            \ spaces before or after the commas.\n\n6. The output should be a clean,\
            \ comma-separated list of technical keywords without any additional formatting\
            \ or XML tags.\n\n7. Double-check the list to ensure that all relevant\
            \ technical keywords have been included and that the formatting is correct.\n\
            \n8. Provide the final output as a simple text string containing the comma-separated\
            \ list of technical keywords.\n\n## Examples\n\n### Example 1\n\n**Resume**\
            \  \nJohn Doe is a software engineer with experience in Java, Python,\
            \ and C++. He has worked with frameworks such as Flask and Django, and\
            \ is proficient in using AWS for cloud services. John is also skilled\
            \ in database management with MySQL and PostgreSQL, and uses Git for version\
            \ control.\n\n**Keywords**  \nJava, Python, C++, Flask, Django, AWS, MySQL,\
            \ PostgreSQL, Git\n\n### Example 2\n\n**Resume**  \nJane Smith is a DevOps\
            \ engineer who specializes in containerization and orchestration using\
            \ Docker and Kubernetes. She has extensive experience with CI/CD pipelines\
            \ using Jenkins and Maven, and is familiar with cloud platforms like Azure\
            \ and GCP. Jane also has a background in network security and uses tools\
            \ like Ansible and Terraform.\n\n**Keywords**  \nDocker, Kubernetes, Jenkins,\
            \ Maven, Azure, GCP, Ansible, Terraform\n\n### Example 3\n\n**Resume**\
            \  \nMichael Brown is a data scientist with expertise in machine learning\
            \ and data analysis. He is proficient in Python and R, and uses libraries\
            \ such as TensorFlow and scikit-learn. Michael has experience with big\
            \ data technologies like Hadoop and Spark, and is skilled in data visualization\
            \ tools like Tableau and Power BI.\n\n**Keywords**  \nPython, R, TensorFlow,\
            \ scikit-learn, Hadoop, Spark, Tableau, Power BI\n"
        - id: 83cc6959-63d4-4da2-bf13-bbcdabe22ed5
          role: user
          text: '{{#context#}}'
        selected: false
        title: Resume Keyword Extractor
        type: llm
        variables: []
        vision:
          configs:
            detail: high
          enabled: true
      height: 96
      id: '1728190596485'
      position:
        x: 448
        y: 500.8289470147241
      positionAbsolute:
        x: 448
        y: 500.8289470147241
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1728190946806'
          - text
          variable: text
        selected: false
        title: End
        type: end
      height: 88
      id: '1728190903113'
      position:
        x: 2034.2857918774616
        y: 365.8289470147243
      positionAbsolute:
        x: 2034.2857918774616
        y: 365.8289470147243
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: gpt-4o-2024-08-06
          provider: openai
        prompt_template:
        - id: e98089b3-0e3b-42f3-a97e-d0db1004d273
          role: system
          text: '# Instructions


            1. Begin by reviewing the two sets of technical keywords provided: one
            from the job description and the other from the resume. These keywords
            are crucial for determining the compatibility between the candidate and
            the job.


            2. Compare the two sets of keywords. Identify which keywords appear in
            both sets, as these indicate a match between the candidate''s skills and
            the job requirements.


            3. Highlight any keywords from the job description that are missing in
            the resume''s keyword set. These represent potential gaps in the candidate''s
            qualifications for the job.


            4. Similarly, note any keywords present in the resume but absent from
            the job description. These may indicate additional skills the candidate
            possesses that are not explicitly required for the job.


            5. Use the reference job description and resume to provide context for
            your analysis. This will help you understand the importance of each keyword
            and how it relates to the overall job requirements and candidate''s experience.


            6. Based on your comparison, provide a summary analysis that states whether
            the candidate matches the job requirements. Consider both the presence
            of matching keywords and the significance of any missing or additional
            keywords.


            7. Ensure that your output is clear and concise, focusing on the key points
            of your analysis. Avoid using any XML tags in your output.


            8. Conclude with a recommendation on the candidate''s suitability for
            the job based on the keyword analysis.


            # Examples


            ## Example 1


            **Input:**

            - Job Description Keywords: Java, Python, SQL, Team Leadership

            - Resume Keywords: Java, Python, SQL, Project Management

            - Job Description Reference: <job description text pasted>

            - Resume Reference: <resume text pasted>


            **Output:**

            The candidate matches the job requirements well, with a strong alignment
            in Java, Python, and SQL skills. However, the job description emphasizes
            team leadership, which is not explicitly mentioned in the resume. The
            candidate''s project management skills could be an asset, but the lack
            of team leadership experience might be a concern. Overall, the candidate
            is a good fit, but further discussion on leadership experience is recommended.


            ## Example 2


            **Input:**

            - Job Description Keywords: C++, Machine Learning, Data Analysis, Communication
            Skills

            - Resume Keywords: C++, Machine Learning, Data Visualization, Communication
            Skills

            - Job Description Reference: <job description text pasted>

            - Resume Reference: <resume text pasted>


            **Output:**

            The candidate demonstrates a strong match with the job requirements, particularly
            in C++, machine learning, and communication skills. The resume highlights
            data visualization skills, which could complement the data analysis requirement
            in the job description. However, the explicit mention of data analysis
            is missing. The candidate appears to be a good fit, but it would be beneficial
            to discuss their experience in data analysis further.'
        - id: dbf8849f-ade7-48db-9efa-6e3bc729e9b6
          role: user
          text: '{{#1728224784436.output#}}'
        selected: false
        title: Keywords Match LLM
        type: llm
        variables: []
        vision:
          configs:
            detail: high
          enabled: true
      height: 96
      id: '1728190930023'
      position:
        x: 1164.7211371447063
        y: 389.951612469662
      positionAbsolute:
        x: 1164.7211371447063
        y: 389.951612469662
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: gpt-4o-2024-08-06
          provider: openai
        prompt_template:
        - id: 60037f94-cba9-40d3-a969-94195ff7c4d6
          role: system
          text: "You are given two analysis of a job application, both analyzed if\
            \ the candidate matches the position. First analysis is performed by directly\
            \ checking the job description and the resume. Second analysis is performed\
            \ by checking if the technical keywords mentioned in the job description\
            \ appears in the resume. \n\nNow your job is to combine both analysis\
            \ and give a final analysis, as well as advice to such candidate about\
            \ potential improvement"
        - id: e871a19b-cf29-4c55-9794-a0a174f44d01
          role: user
          text: 'First analysis by direct analysis:


            ---


            {{#1728189832414.text#}}


            ---


            Second analysis by keyword comparison:


            ---


            {{#1728190930023.text#}}


            ___'
        selected: true
        title: Final Analysis LLM
        type: llm
        variables: []
        vision:
          configs:
            detail: high
          enabled: true
      height: 96
      id: '1728190946806'
      position:
        x: 1667.5946509141907
        y: 365.8289470147243
      positionAbsolute:
        x: 1667.5946509141907
        y: 365.8289470147243
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        desc: ''
        selected: false
        template: "Here is the Job Description:\r\n\r\n---\r\n\r\n{{ jd }}\r\n\r\n\
          ---\r\n\r\nHere is the resume:\r\n\r\n---\r\n\r\n{{ resume }}\r\n\r\n---\r\
          \n\r\nHere are the keyword sets: \r\n- Job Description Keywords: {{ jd_keywords\
          \ }}\r\n- Resume Keywords: {{ resume_keywords }}"
        title: Prompt Combiner
        type: template-transform
        variables:
        - value_selector:
          - '1728189789518'
          - jd
          variable: jd
        - value_selector:
          - '1728189789518'
          - resume
          variable: resume
        - value_selector:
          - '1728190110426'
          - text
          variable: jd_keywords
        - value_selector:
          - '1728190596485'
          - text
          variable: resume_keywords
      height: 52
      id: '1728224784436'
      position:
        x: 845.6953180033437
        y: 389.951612469662
      positionAbsolute:
        x: 845.6953180033437
        y: 389.951612469662
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    viewport:
      x: -14.22053546470579
      y: 38.74097706808993
      zoom: 0.8705505632961241
