app:
  description: ''
  icon: 🔎
  icon_background: '#D1E0FF'
  mode: workflow
  name: Web Content Search and Crawl
  use_icon_as_answer_icon: false
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 100
        batch_count_limit: 10
        file_size_limit: 250
        image_file_size_limit: 50
        video_file_size_limit: 500
        workflow_file_upload_limit: 50
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        sourceType: tool
        targetType: code
      id: 1713262010863-1713262060182
      selected: false
      source: '1713262010863'
      sourceHandle: source
      target: '1713262060182'
      targetHandle: target
      type: custom
    - data:
        sourceType: start
        targetType: tool
      id: 1713261835258-1713262010863
      source: '1713261835258'
      sourceHandle: source
      target: '1713262010863'
      targetHandle: target
      type: custom
    - data:
        isInIteration: true
        iteration_id: '1716911333343'
        sourceType: iteration-start
        targetType: tool
      id: 1716911333343start0-source-1720758285748-target
      source: 1716911333343start0
      sourceHandle: source
      target: '1720758285748'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        sourceType: code
        targetType: iteration
      id: 1713262060182-source-1716911333343-target
      source: '1713262060182'
      sourceHandle: source
      target: '1716911333343'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: true
        iteration_id: '1716911333343'
        sourceType: tool
        targetType: code
      id: 1720758285748-source-1739656266970-target
      source: '1720758285748'
      sourceHandle: source
      target: '1739656266970'
      targetHandle: target
      type: custom
      zIndex: 1002
    - data:
        isInIteration: false
        sourceType: iteration
        targetType: template-transform
      id: 1716911333343-source-1739657507784-target
      source: '1716911333343'
      sourceHandle: source
      target: '1739657507784'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: template-transform
        targetType: end
      id: 1739657507784-source-1739656743266-target
      source: '1739657507784'
      sourceHandle: source
      target: '1739656743266'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: Start
        type: start
        variables:
        - label: Search Query
          max_length: 256
          options: []
          required: true
          type: text-input
          variable: Question
      height: 88
      id: '1713261835258'
      position:
        x: 30
        y: 388.5
      positionAbsolute:
        x: 30
        y: 388.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        desc: ''
        provider_id: tavily
        provider_name: tavily
        provider_type: builtin
        selected: false
        title: TavilySearch
        tool_configurations:
          days: 3
          exclude_domains: null
          include_answer: 0
          include_domains: null
          include_image_descriptions: 0
          include_images: 0
          include_raw_content: 0
          max_results: 5
          search_depth: basic
          topic: general
        tool_label: TavilySearch
        tool_name: tavily_search
        tool_parameters:
          query:
            type: mixed
            value: '{{#1713261835258.Question#}}'
        type: tool
      height: 322
      id: '1713262010863'
      position:
        x: 331.7190223065919
        y: 388.5
      positionAbsolute:
        x: 331.7190223065919
        y: 388.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        code: "import json\nfrom typing import List\n\ndef main(data: List[dict])\
          \ -> dict:\n    # data_dict = json.loads(data)\n    print(data)\n    urls\
          \ = []\n    for action in data:\n        for result in action['results']:\n\
          \            urls.append(result['url'])\n    return {'result': urls}"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: array[string]
        selected: false
        title: Extract URLs
        type: code
        variables:
        - value_selector:
          - '1713262010863'
          - json
          variable: data
      height: 52
      id: '1713262060182'
      position:
        x: 638
        y: 388.5
      positionAbsolute:
        x: 638
        y: 388.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        desc: ''
        error_handle_mode: remove-abnormal-output
        height: 531
        is_parallel: true
        iterator_selector:
        - '1713262060182'
        - result
        output_selector:
        - '1739656266970'
        - result
        output_type: array[object]
        parallel_nums: 10
        selected: false
        startNodeType: tool
        start_node_id: 1716911333343start0
        title: Read URLs Iteration
        type: iteration
        width: 985
      height: 531
      id: '1716911333343'
      position:
        x: 940.8595111532959
        y: 388.5
      positionAbsolute:
        x: 940.8595111532959
        y: 388.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 985
      zIndex: 1
    - data:
        desc: ''
        isInIteration: true
        isIterationStart: true
        iteration_id: '1716911333343'
        provider_id: jina
        provider_name: jina
        provider_type: builtin
        retry_config:
          max_retries: 3
          retry_enabled: true
          retry_interval: 1000
        selected: false
        title: JinaReader
        tool_configurations:
          gather_all_images_at_the_end: 1
          gather_all_links_at_the_end: 1
          image_caption: 1
          max_retries: 3
          no_cache: 0
          proxy_server: null
          remove_selector: null
          retain_images: 0
          summary: 0
          target_selector: null
          wait_for_selector: null
          with_iframe: 1
          with_shadow_dom: 1
        tool_label: JinaReader
        tool_name: jina_reader
        tool_parameters:
          request_params:
            type: mixed
            value: ''
          url:
            type: mixed
            value: '{{#1716911333343.item#}}'
        type: tool
      extent: parent
      height: 426
      id: '1720758285748'
      parentId: '1716911333343'
      position:
        x: 117
        y: 85
      positionAbsolute:
        x: 1057.859511153296
        y: 473.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
      zIndex: 1001
    - data:
        desc: ''
        isInIteration: true
        selected: false
        title: ''
        type: iteration-start
      draggable: false
      height: 48
      id: 1716911333343start0
      parentId: '1716911333343'
      position:
        x: 24
        y: 68
      positionAbsolute:
        x: 964.8595111532959
        y: 456.5
      selectable: false
      sourcePosition: right
      targetPosition: left
      type: custom-iteration-start
      width: 44
      zIndex: 1002
    - data:
        code: "import json\n\ndef main(json_str: str) -> dict:\n    data = json.loads(json_str)['data']\n\
          \    info = {\n        \"title\": data['title'],\n        \"description\"\
          : data[\"description\"],\n        \"url\": data[\"url\"],\n        \"content\"\
          : data[\"content\"]\n    }\n    return {\n        \"result\": info\n   \
          \ }"
        code_language: python3
        desc: ''
        isInIteration: true
        iteration_id: '1716911333343'
        outputs:
          result:
            children: null
            type: object
        selected: false
        title: Extract Read Result
        type: code
        variables:
        - value_selector:
          - '1720758285748'
          - text
          variable: json_str
      extent: parent
      height: 52
      id: '1739656266970'
      parentId: '1716911333343'
      position:
        x: 499.8342192692803
        y: 85
      positionAbsolute:
        x: 1440.6937304225762
        y: 473.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
      zIndex: 1002
    - data:
        desc: ''
        outputs:
        - value_selector:
          - '1739657507784'
          - output
          variable: output
        selected: false
        title: End
        type: end
      height: 88
      id: '1739656743266'
      position:
        x: 2339.462486948384
        y: 388.5
      positionAbsolute:
        x: 2339.462486948384
        y: 388.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        desc: ''
        selected: false
        template: "Below are the results of \"{{ query }}\":\r\n{% for item in data\
          \ %}\r\n\r\n---\r\n\r\ntitle: {{ item.title }}\r\nurl: {{ item.url }}\r\n\
          description: {{ item.description }}\r\n\r\n{{ item.content }}\r\n\r\n{%\
          \ endfor %}"
        title: Aggregate and Format Results
        type: template-transform
        variables:
        - value_selector:
          - '1716911333343'
          - output
          variable: data
        - value_selector:
          - '1713261835258'
          - Question
          variable: query
      height: 52
      id: '1739657507784'
      position:
        x: 1985.859511153296
        y: 388.5
      positionAbsolute:
        x: 1985.859511153296
        y: 388.5
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    viewport:
      x: 38.18627471848504
      y: -144.22334904889152
      zoom: 0.8768169920205584
