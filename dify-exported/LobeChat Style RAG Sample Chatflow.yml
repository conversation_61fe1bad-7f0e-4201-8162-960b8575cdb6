app:
  description: ''
  icon: exploding_head
  icon_background: '#FEF7C3'
  mode: advanced-chat
  name: LobeChat Style RAG Sample Chatflow
  use_icon_as_answer_icon: true
kind: app
version: 0.1.5
workflow:
  conversation_variables:
  - description: Is this the beginning of the conversation
    id: b865f69b-0c07-4567-a6b9-5933f1d9757c
    name: is_beginning
    selector:
    - conversation
    - is_beginning
    value: 1
    value_type: number
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 100
        batch_count_limit: 10
        file_size_limit: 250
        image_file_size_limit: 50
        video_file_size_limit: 500
        workflow_file_upload_limit: 50
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        sourceType: knowledge-retrieval
        targetType: llm
      id: knowledge_retrieval-llm
      selected: false
      source: knowledge_retrieval
      sourceHandle: source
      target: llm
      targetHandle: target
      type: custom
    - data:
        sourceType: llm
        targetType: answer
      id: llm-answer
      selected: false
      source: llm
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
    - data:
        isInIteration: false
        sourceType: start
        targetType: if-else
      id: start-source-1732226863092-target
      source: start
      sourceHandle: source
      target: '1732226863092'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: variable-aggregator
        targetType: knowledge-retrieval
      id: 1732226985294-source-knowledge_retrieval-target
      source: '1732226985294'
      sourceHandle: source
      target: knowledge_retrieval
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: llm
        targetType: variable-aggregator
      id: 1726257187350-source-1732226985294-target
      source: '1726257187350'
      sourceHandle: source
      target: '1732226985294'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: llm
      id: 1732226863092-false-1726257187350-target
      source: '1732226863092'
      sourceHandle: 'false'
      target: '1726257187350'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: template-transform
      id: 1732226863092-true-1732227230273-target
      source: '1732226863092'
      sourceHandle: 'true'
      target: '1732227230273'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: template-transform
        targetType: variable-aggregator
      id: 1732227230273-source-1732226985294-target
      source: '1732227230273'
      sourceHandle: source
      target: '1732226985294'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        sourceType: if-else
        targetType: assigner
      id: 1732226863092-true-1732227063024-target
      source: '1732226863092'
      sourceHandle: 'true'
      target: '1732227063024'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        selected: false
        title: START
        type: start
        variables: []
      height: 52
      id: start
      position:
        x: -14.19326510975688
        y: 282
      positionAbsolute:
        x: -14.19326510975688
        y: 282
      selected: false
      type: custom
      width: 243
    - data:
        dataset_ids:
        - 7cb7eee4-0087-4929-b415-59deef6846d2
        multiple_retrieval_config:
          reranking_enable: true
          reranking_mode: reranking_model
          reranking_model:
            model: rerank-english-v3.0
            provider: cohere
          score_threshold: null
          top_k: 5
          weights:
            keyword_setting:
              keyword_weight: 0.3
            vector_setting:
              embedding_model_name: text-embedding-3-large
              embedding_provider_name: openai
              vector_weight: 0.7
        query_variable_selector:
        - '1732226985294'
        - output
        retrieval_mode: multiple
        selected: false
        single_retrieval_config: null
        title: KNOWLEDGE RETRIEVAL
        type: knowledge-retrieval
      height: 90
      id: knowledge_retrieval
      position:
        x: 1641.839981824066
        y: 300.72495792028474
      positionAbsolute:
        x: 1641.839981824066
        y: 300.72495792028474
      selected: false
      type: custom
      width: 243
    - data:
        context:
          enabled: true
          variable_selector:
          - knowledge_retrieval
          - result
        memory:
          query_prompt_template: '{{#sys.query#}}'
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
        model:
          completion_params:
            stop: []
          mode: chat
          name: gemini-2.0-pro-exp-02-05
          provider: google
        prompt_template:
        - id: 9f5489ef-24cb-43f6-bf27-5e3e206055c3
          role: user
          text: 'Use the following context as your learned knowledge, inside <context></context>
            XML tags.


            <context>

            {{#context#}}

            </context>


            When answer to user:

            - If you don''t know, just say that you don''t know.

            - If you don''t know when you are not sure, ask for clarification.

            Avoid mentioning that you obtained the information from the context.

            And answer according to the language of the user''s question.

            '
        selected: true
        title: LLM
        type: llm
        vision:
          configs:
            detail: high
            variable_selector:
            - sys
            - files
          enabled: false
      height: 96
      id: llm
      position:
        x: 2002.7209946389085
        y: 300.72495792028474
      positionAbsolute:
        x: 2002.7209946389085
        y: 300.72495792028474
      selected: true
      type: custom
      width: 243
    - data:
        answer: '{{#llm.text#}}'
        selected: false
        title: ANSWER
        type: answer
      height: 100
      id: answer
      position:
        x: 2384.5083175146983
        y: 300.72495792028474
      positionAbsolute:
        x: 2384.5083175146983
        y: 300.72495792028474
      selected: false
      type: custom
      width: 243
    - data:
        context:
          enabled: true
          variable_selector:
          - sys
          - query
        desc: ''
        memory:
          query_prompt_template: "Everything above in this conversation are the conversation\
            \ history. Now here is my query: \n\n{{#sys.query#}}"
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 50
        model:
          completion_params:
            temperature: 0.5
          mode: chat
          name: gpt-4o-2024-08-06
          provider: openai
        prompt_template:
        - id: a115e4e8-ff8c-456d-a678-ae9a3be05b26
          role: system
          text: Given the following conversation and a follow-up question, rephrase
            the follow-up question to be a standalone question, in its original language.
            Keep as much details as possible from previous messages. Keep entity names
            and all.
        selected: false
        title: Query Reformatting LLM
        type: llm
        variables: []
        vision:
          configs:
            detail: high
            variable_selector:
            - sys
            - files
          enabled: true
      height: 96
      id: '1726257187350'
      position:
        x: 783.382851949733
        y: 396.8698354997034
      positionAbsolute:
        x: 783.382851949733
        y: 396.8698354997034
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: '='
            id: e446bf3b-5012-491d-8700-4c78d1c54273
            numberVarType: constant
            value: '1'
            varType: number
            variable_selector:
            - conversation
            - is_beginning
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: IF/ELSE
        type: if-else
      height: 124
      id: '1732226863092'
      position:
        x: 314.52419376516895
        y: 282
      positionAbsolute:
        x: 314.52419376516895
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        advanced_settings:
          group_enabled: false
          groups:
          - groupId: fb45ca7c-75f7-45fb-98a9-588e997a5375
            group_name: Group1
            output_type: string
            variables:
            - - '1726257187350'
              - text
        desc: ''
        output_type: string
        selected: false
        title: Query Aggregator
        type: variable-aggregator
        variables:
        - - '1726257187350'
          - text
        - - '1732227230273'
          - output
      height: 129
      id: '1732226985294'
      position:
        x: 1231.754669090124
        y: 300.72495792028474
      positionAbsolute:
        x: 1231.754669090124
        y: 300.72495792028474
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        assigned_variable_selector:
        - conversation
        - is_beginning
        desc: ''
        input_variable_selector:
        - '1732227365172'
        - zero
        selected: false
        title: Variable Assigner
        type: assigner
        write_mode: clear
      height: 86
      id: '1732227063024'
      position:
        x: 783.382851949733
        y: 68.34210597055156
      positionAbsolute:
        x: 783.382851949733
        y: 68.34210597055156
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        desc: ''
        selected: false
        template: '{{ orig_query }}'
        title: Origin Query to Variable
        type: template-transform
        variables:
        - value_selector:
          - sys
          - query
          variable: orig_query
      height: 52
      id: '1732227230273'
      position:
        x: 783.382851949733
        y: 247.88483359042112
      positionAbsolute:
        x: 783.382851949733
        y: 247.88483359042112
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 243
    - data:
        author: CXwudi
        desc: ''
        height: 129
        selected: false
        showAuthor: true
        text: '{"root":{"children":[{"children":[{"detail":0,"format":0,"mode":"normal","style":"font-size:
          14px;","text":"This is the simulation of LobeChat style RAG procedure. This
          is pretty much just Dify RAG but added the query rephrasing procedure that
          LobeChat has.","type":"text","version":1}],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1,"textFormat":0}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}'
        theme: blue
        title: ''
        type: ''
        width: 381
      height: 129
      id: '1732235058086'
      position:
        x: 272.9387154004619
        y: 56.65817904104371
      positionAbsolute:
        x: 272.9387154004619
        y: 56.65817904104371
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom-note
      width: 381
    viewport:
      x: -951.6069524371924
      y: 151.67619032048674
      zoom: 0.8705505632961242
