#!/bin/bash

# Create containers
docker compose -f compose.base.yml create

# Restore qBittorrent data
docker run --rm --volumes-from qbittorrent-app -v $(pwd)/backup:/backup debian bash -c "rm -rf /config/* && tar xvf /backup/qbittorrent-config.tar -C /"

# Restore PeerBanHelper data
docker run --rm --volumes-from qbittorrent-peerbanhelper -v $(pwd)/backup:/backup debian bash -c "rm -rf /app/data/* && tar xvf /backup/peerbanhelper-data.tar -C /"

# Remove containers
docker compose -f compose.base.yml down

echo "Backup restoration completed."
