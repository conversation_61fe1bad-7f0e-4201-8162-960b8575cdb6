#!/bin/bash

# Create containers
docker compose -f compose.base.yml create

# Create backup
docker run --rm --volumes-from qbittorrent-app -v $(pwd)/backup:/backup debian tar cvf /backup/qbittorrent-config.tar /config
docker run --rm --volumes-from qbittorrent-peerbanhelper -v $(pwd)/backup:/backup debian tar cvf /backup/peerbanhelper-data.tar /app/data

# Remove containers
docker compose -f compose.base.yml down

# Change ownership
sudo chown -R $USER:$USER backup

echo "Backup created."
