server {
    listen              443 ssl;
    listen              [::]:443 ssl;
    http2               on;
    server_name         dify.mikufancx.cyou;

    # SSL
    ssl_certificate     /etc/ssl/_.mikufancx.cyou.crt;
    ssl_certificate_key /etc/ssl/_.mikufancx.cyou.key;

    # security
    include             nginxconfig.io/security.conf;

    # logging
    access_log          /var/log/nginx/dify.mikufancx.cyou.access.log cloudflare buffer=512k flush=1m;
    error_log           /var/log/nginx/dify.mikufancx.cyou.error.log warn;

    # docker DNS resolver
    resolver              127.0.0.11 [::1]:5353 valid=15s;
    set                   $backend "http://dify-nginx";

    # reverse proxy
    location / {
        proxy_pass            $backend;
        proxy_set_header Host $host;
        include               nginxconfig.io/proxy.conf;
    }

    # additional config
    include nginxconfig.io/general.conf;
}

# HTTP redirect
server {
    listen      80;
    listen      [::]:80;
    server_name dify.mikufancx.cyou;
    return      301 https://dify.mikufancx.cyou$request_uri;
}