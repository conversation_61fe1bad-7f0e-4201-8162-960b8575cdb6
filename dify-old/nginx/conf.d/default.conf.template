# Please do not directly edit this file. Instead, modify the .env variables related to NGINX configuration.

server {
    listen ${NGINX_PORT};
    server_name ${NGINX_SERVER_NAME};

    resolver              127.0.0.11 [::1]:5353 valid=15s;
    set                   $api_backend "http://api:5001";
    set                   $web_backend "http://web:3000";
    location /console/api {
      proxy_pass $api_backend;
      include proxy.conf;
    }

    location /api {
      proxy_pass $api_backend;
      include proxy.conf;
    }

    location /v1 {
      proxy_pass $api_backend;
      include proxy.conf;
    }

    location /files {
      proxy_pass $api_backend;
      include proxy.conf;
    }

    location / {
      proxy_pass $web_backend;
      include proxy.conf;
    }

    # placeholder for https config defined in https.conf.template
    ${HTTPS_CONFIG}
}
