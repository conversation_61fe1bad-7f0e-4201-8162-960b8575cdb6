#!/bin/bash

if [ "${NGINX_HTTPS_ENABLED}" = "true" ]; then
    # set the HTTPS_CONFIG environment variable to the content of the https.conf.template
    export HTTPS_CONFIG=$(envsubst < /etc/nginx/https.conf.template)
else
    export HTTPS_CONFIG=""
fi


env_vars=$(printenv | cut -d= -f1 | sed 's/^/$/g' | paste -sd, -)

envsubst "$env_vars" < /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf
envsubst "$env_vars" < /etc/nginx/proxy.conf.template > /etc/nginx/proxy.conf

envsubst "$env_vars ${HTTPS_CONFIG}"< /etc/nginx/conf.d/default.conf.template > /etc/nginx/conf.d/default.conf

# Start Nginx using the default entrypoint
exec nginx -g 'daemon off;'