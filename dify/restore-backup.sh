#!/bin/bash

# Check if backup directory exists and contains the required files
if [ ! -d "backup" ]; then
    echo "Error: backup directory not found"
    exit 1
fi

for file in dify-weaviate.tar dify-redis.tar dify-db.tar dify-app-storage.tar; do
    if [ ! -f "backup/$file" ]; then
        echo "Error: backup/$file not found"
        exit 1
    fi
done

# Create containers
docker compose -f compose.base.yml create

# Restore Weaviate data
docker run --rm --volumes-from dify-weaviate -v $(pwd)/backup:/backup debian bash -c "rm -rf /var/lib/weaviate/* && tar xvf /backup/dify-weaviate.tar -C /"

# Restore Redis data
docker run --rm --volumes-from dify-redis -v $(pwd)/backup:/backup debian bash -c "rm -rf /data/* && tar xvf /backup/dify-redis.tar -C /"

# Restore PostgreSQL data
docker run --rm --volumes-from dify-db -v $(pwd)/backup:/backup debian bash -c "rm -rf /var/lib/postgresql/data/* && tar xvf /backup/dify-db.tar -C /"

# Restore local storage
docker run --rm --volumes-from dify-api -v $(pwd)/backup:/backup debian bash -c "rm -rf /app/api/storage/* && tar xvf /backup/dify-app-storage.tar -C /"

# Remove containers
docker compose -f compose.base.yml down

echo "Backup restoration completed successfully."
