http:
  pprof:
    port: 6060
    enabled: false
  address: 0.0.0.0:3000
  session_ttl: 720h
users:
  - name: <PERSON><PERSON><PERSON><PERSON>
    password: $2a$10$8yT.21AJpIsk9yeuYQl8fudGNCZiB4RulM3f2W/9Bk8smP/HADrx.
auth_attempts: 5
block_auth_min: 15
http_proxy: ""
language: ""
theme: auto
dns:
  bind_hosts:
    - 0.0.0.0
  port: 53
  anonymize_client_ip: false
  ratelimit: 20
  ratelimit_subnet_len_ipv4: 24
  ratelimit_subnet_len_ipv6: 56
  ratelimit_whitelist: []
  refuse_any: true
  upstream_dns:
    - '# Private rDNS'
    - '[/in-addr.arpa/]***********'
    - '[/ip6.arpa/]***********'
    - '# it is generally safe to let OpenWrt''s upstream DNS to resolve our own domains'
    - '[/mikufancx.cyou/]***********'
    - '# Public DNS'
    - '# all DNS are TLS or QUIC, without any anti-phishing or anti-ads'
    - '# Cloudflare DNS'
    - https://dns.cloudflare.com/dns-query
    - '# AdGuard DNS'
    - quic://unfiltered.adguard-dns.com
    - h3://unfiltered.adguard-dns.com/dns-query
    - '# Tencent DNS'
    - https://doh.pub/dns-query
  upstream_dns_file: ""
  bootstrap_dns:
    - *******
    - *******
    - 2620:fe::10
    - 2620:fe::fe:10
  fallback_dns: []
  upstream_mode: load_balance
  fastest_timeout: 1s
  allowed_clients: []
  disallowed_clients: []
  blocked_hosts:
    - version.bind
    - id.server
    - hostname.bind
  trusted_proxies:
    - *********/8
    - ::1/128
  cache_size: 4194304
  cache_ttl_min: 0
  cache_ttl_max: 0
  cache_optimistic: false
  bogus_nxdomain: []
  aaaa_disabled: false
  enable_dnssec: false
  edns_client_subnet:
    custom_ip: ""
    enabled: false
    use_custom: false
  max_goroutines: 300
  handle_ddr: true
  ipset: []
  ipset_file: ""
  bootstrap_prefer_ipv6: false
  upstream_timeout: 10s
  private_networks: []
  use_private_ptr_resolvers: true
  local_ptr_upstreams:
    - ***********
  use_dns64: false
  dns64_prefixes: []
  serve_http3: false
  use_http3_upstreams: false
  serve_plain_dns: true
  hostsfile_enabled: true
tls:
  enabled: false
  server_name: ""
  force_https: false
  port_https: 443
  port_dns_over_tls: 853
  port_dns_over_quic: 853
  port_dnscrypt: 0
  dnscrypt_config_file: ""
  allow_unencrypted_doh: false
  certificate_chain: ""
  private_key: ""
  certificate_path: ""
  private_key_path: ""
  strict_sni_check: false
querylog:
  dir_path: ""
  ignored: []
  interval: 168h
  size_memory: 1000
  enabled: true
  file_enabled: true
statistics:
  dir_path: ""
  ignored: []
  interval: 24h
  enabled: true
filters:
  - enabled: true
    url: https://adguardteam.github.io/HostlistsRegistry/assets/filter_1.txt
    name: AdGuard DNS filter
    id: 1
  - enabled: true
    url: https://raw.githubusercontent.com/crazy-max/WindowsSpyBlocker/master/data/hosts/spy.txt
    name: WindowsSpyBlocker - Hosts spy rules
    id: 2
  - enabled: true
    url: https://raw.githubusercontent.com/hoshsadiq/adblock-nocoin-list/master/hosts.txt
    name: NoCoin Filter List
    id: 3
  - enabled: true
    url: https://malware-filter.gitlab.io/malware-filter/phishing-filter-agh.txt
    name: Phishing URL Blocklist (PhishTank and OpenPhish)
    id: 4
  - enabled: true
    url: https://anti-ad.net/easylist.txt
    name: 'CHN: anti-AD'
    id: 5
  - enabled: true
    url: https://raw.githubusercontent.com/hl2guide/AdGuard-Home-Whitelist/main/whitelist.txt
    name: AdGuard-Home-Whitelist
    id: 6
whitelist_filters: []
user_rules:
  - '# unblock torrent'
  - '@@||tracker.lilithraws.cf^$important'
  - '@@||tracker.cubonegro.lol^$important'
  - '# this unblocks rss feed, important for ttrss'
  - '@@||feeds.feedblitz.com^$important'
  - '@@||www.feedblitz.com^$important'
  - '@@||t.overflow.biz^$important'
  - '# website for 千本桜'
  - '@@||kyoto-ohara-kankouhosyoukai.net^$important'
  - '@@||landing.service.newrelic.com^$important'
  - '@@||sentry.io^$important'
  - '# songle'
  - '@@||widget.songle.jp^$important'
  - '@@||bit.ly^$important'
  - '# xvideo'
  - '@@||cdn77-pic.xvideos-cdn.com^$important'
  - '# for codex games'
  - '@@||filecrypt.cc^$important'
  - '@@||filecrypt.co^$important'
  - '# a japanese website'
  - '@@||search-voi.0101.co.jp^$important'
  - '# '
  - '@@||figshare.com^$important'
  - '# unblock job search'
  - '@@||www.stackadapt.com^$important'
  - '# facebook/messenger is broken, see https://github.com/privacy-protection-tools/anti-AD/issues/895 and https://github.com/privacy-protection-tools/anti-AD/issues/892'
  - '# @@||facebook.com^$important'
  - '# @@||messenger.com^$important'
  - '# unblock RBC''s MFA login'
  - '@@||d3tracking.rbc.com^$important'
  - '# wechat friends need it to load image, espacially good lookng women''s image ( ͡° ͜ʖ ͡°)'
  - '@@||canvas.gdt.qq.com^$important'
  - '# unblock scotia bank 2-way setup'
  - '@@||sb.scorecardresearch.com^$important'
  - '@@||scotiabank.sc.omtrdc.net^$important'
  - '@@||9n3bwgl2.r.us-west-2.awstrack.me^$important'
  - '# unstructured for ETL for LLM'
  - '@@||downloads.unstructured.io^$important'
  - ""
dhcp:
  enabled: false
  interface_name: ""
  local_domain_name: lan
  dhcpv4:
    gateway_ip: ""
    subnet_mask: ""
    range_start: ""
    range_end: ""
    lease_duration: 86400
    icmp_timeout_msec: 1000
    options: []
  dhcpv6:
    range_start: ""
    lease_duration: 86400
    ra_slaac_only: false
    ra_allow_slaac: false
filtering:
  blocking_ipv4: ""
  blocking_ipv6: ""
  blocked_services:
    schedule:
      time_zone: America/New_York
    ids: []
  protection_disabled_until: null
  safe_search:
    enabled: false
    bing: true
    duckduckgo: true
    google: true
    pixabay: true
    yandex: true
    youtube: true
  blocking_mode: default
  parental_block_host: family-block.dns.adguard.com
  safebrowsing_block_host: standard-block.dns.adguard.com
  rewrites: []
  safebrowsing_cache_size: 1048576
  safesearch_cache_size: 1048576
  parental_cache_size: 1048576
  cache_time: 30
  filters_update_interval: 24
  blocked_response_ttl: 10
  filtering_enabled: true
  parental_enabled: false
  safebrowsing_enabled: false
  protection_enabled: true
clients:
  runtime_sources:
    whois: true
    arp: true
    rdns: true
    dhcp: true
    hosts: true
  persistent: []
log:
  enabled: true
  file: ""
  max_backups: 0
  max_size: 100
  max_age: 3
  compress: false
  local_time: false
  verbose: false
os:
  group: ""
  user: ""
  rlimit_nofile: 0
schema_version: 28
